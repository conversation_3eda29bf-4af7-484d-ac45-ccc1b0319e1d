// Test script to verify the multi-therapist TypeError fix
import { OpenAIService } from './src/services/openai';
import { MultiTherapistAgentService } from './src/services/agents/multi-therapist';
import { MultiTherapistConversationOrchestrator } from './src/services/multi-therapist-orchestrator';
import { defaultConversationConfig } from './src/config/conversation';

async function testMultiTherapistInitialization() {
  console.log('🧪 Testing MultiTherapist initialization...');
  
  try {
    // Test 1: MultiTherapistAgentService initialization
    console.log('1. Testing MultiTherapistAgentService...');
    const openaiService = new OpenAIService();
    const multiTherapistAgent = new MultiTherapistAgentService(openaiService);
    console.log('✅ MultiTherapistAgentService initialized successfully');
    
    // Test 2: MultiTherapistConversationOrchestrator initialization
    console.log('2. Testing MultiTherapistConversationOrchestrator...');
    const orchestrator = new MultiTherapistConversationOrchestrator(
      'test-conversation-id',
      defaultConversationConfig,
      'test-persona'
    );
    console.log('✅ MultiTherapistConversationOrchestrator initialized successfully');
    
    // Test 3: Generate initial greetings
    console.log('3. Testing initial greetings generation...');
    const initialGreetings = await multiTherapistAgent.generateInitialGreetings();
    console.log('✅ Initial greetings generated successfully');
    console.log('📝 Generated greetings:', {
      cbtOnly: initialGreetings.cbtOnly.content.substring(0, 100) + '...',
      miFixedPretreatment: initialGreetings.miFixedPretreatment.content.substring(0, 100) + '...',
      dynamicAdaptive: initialGreetings.dynamicAdaptive.content.substring(0, 100) + '...'
    });
    
    console.log('🎉 All tests passed! The TypeError has been resolved.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
testMultiTherapistInitialization();
