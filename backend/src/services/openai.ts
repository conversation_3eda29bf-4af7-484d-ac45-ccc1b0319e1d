// OpenAI Service for MiCA Therapy Simulation
import OpenAI from 'openai';
import { OpenAIRequest, AgentResponse, ReadinessScore, PatientAnalysis } from '../types/index.js';
import { readinessWeights } from '../data/therapeutic-approaches.js';
import {
  openaiLogger,
  logFunctionEntry,
  logFunctionSuccess,
  logFunctionError,
  logOpenAIUsage,
  logOpenAIResponse,
  logOpenAIRequest,
} from '../utils/logger.js';

export class OpenAIService {
  private client: OpenAI;
  private defaultModel: string;
  private defaultTemperature: number;
  private defaultMaxTokens: number;

  constructor() {
    logFunctionEntry(openaiLogger, 'constructor');

    const apiKey = process.env['OPENAI_API_KEY'];
    if (!apiKey) {
      const error = new Error('OPENAI_API_KEY environment variable is required');
      logFunctionError(openaiLogger, 'constructor', error);
      throw error;
    }

    this.client = new OpenAI({
      apiKey: apiKey,
    });

    this.defaultModel = process.env['OPENAI_MODEL'] || 'gpt-4o-mini';
    this.defaultTemperature = parseFloat(process.env['OPENAI_TEMPERATURE'] || '0.7');
    this.defaultMaxTokens = parseInt(process.env['OPENAI_MAX_TOKENS'] || '500');

    const initConfig = {
      model: this.defaultModel,
      temperature: this.defaultTemperature,
      maxTokens: this.defaultMaxTokens,
    };

    logFunctionSuccess(openaiLogger, 'constructor', initConfig);
    openaiLogger.info(`OpenAI Service initialized successfully`, {
      operation: 'initialization',
      config: initConfig,
    });
  }

  /**
   * Generate a response from OpenAI
   */
  async generateResponse(request: OpenAIRequest): Promise<AgentResponse> {
    const startTime = Date.now();
    const operation = 'generateResponse';

    // Log function entry with sanitized parameters
    logFunctionEntry(openaiLogger, operation, {
      messagesCount: request.messages.length,
      temperature: request.temperature || this.defaultTemperature,
      maxTokens: request.max_completion_tokens || this.defaultMaxTokens,
      model: this.defaultModel,
    });

    try {
      openaiLogger.info('Starting OpenAI chat completion request', {
        operation,
        phase: 'api-call',
        model: this.defaultModel,
        messagesCount: request.messages.length,
        temperature: request.temperature || this.defaultTemperature,
        maxTokens: request.max_completion_tokens || this.defaultMaxTokens,
      });

      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: request.messages,
        temperature: request.temperature || this.defaultTemperature,
        max_completion_tokens: request.max_completion_tokens || this.defaultMaxTokens,
      });

      const processingTime = Date.now() - startTime;
      const responseContent = completion.choices[0]?.message?.content || '';

      // Log API usage and response details
      logOpenAIRequest(openaiLogger, operation, request);
      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);
      logOpenAIResponse(openaiLogger, operation, completion);

      const result: AgentResponse = {
        message: responseContent,
        thinking: '', // Will be populated by agent services
        metadata: {
          confidence: this.calculateConfidence(completion),
          processingTime,
        }
      };

      logFunctionSuccess(openaiLogger, operation, {
        responseLength: responseContent.length,
        confidence: result.metadata.confidence,
        totalTokens: completion.usage?.total_tokens,
      }, processingTime);

      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;

      // Log the error with details
      logFunctionError(openaiLogger, operation, error, processingTime);

      // Handle different types of errors with specific logging
      let errorMessage = 'I apologize, but I\'m having trouble processing your message right now. Could you please try again?';

      if (error instanceof Error) {
        if (error.message.includes('rate limit')) {
          errorMessage = 'OpenAI rate limit exceeded. Please try again later.';
          openaiLogger.warn('OpenAI rate limit exceeded', {
            operation,
            phase: 'rate-limit',
            processingTime: `${processingTime}ms`,
          });
          throw new Error(errorMessage);
        } else if (error.message.includes('insufficient_quota')) {
          errorMessage = 'OpenAI quota exceeded. Please check your billing.';
          openaiLogger.error('OpenAI quota exceeded', {
            operation,
            phase: 'quota-exceeded',
            processingTime: `${processingTime}ms`,
          });
          throw new Error(errorMessage);
        } else if (error.message.includes('invalid_api_key')) {
          errorMessage = 'Invalid OpenAI API key. Please check your configuration.';
          openaiLogger.error('Invalid OpenAI API key', {
            operation,
            phase: 'auth-error',
            processingTime: `${processingTime}ms`,
          });
          throw new Error(errorMessage);
        }
      }

      // Log fallback response
      openaiLogger.info('Returning fallback response due to error', {
        operation,
        phase: 'fallback',
        processingTime: `${processingTime}ms`,
      });

      return {
        message: errorMessage,
        thinking: 'Error occurred while generating response',
        metadata: {
          confidence: 0,
          processingTime,
        }
      };
    }
  }

  /**
   * Generate a thinking process for an agent
   */
  async generateThinking(
    agentType: 'therapist' | 'patient',
    context: string,
    prompt: string
  ): Promise<string> {
    const startTime = Date.now();
    const operation = 'generateThinking';

    logFunctionEntry(openaiLogger, operation, {
      agentType,
      contextLength: context.length,
      promptLength: prompt.length,
    });

    try {
      openaiLogger.info('Starting thinking generation request', {
        operation,
        phase: 'api-call',
        agentType,
        model: this.defaultModel,
        temperature: 0.8,
        maxTokens: 100,
      });

      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: `You are generating internal thoughts for a ${agentType} in a therapy simulation. Provide concise, realistic internal thoughts and reasoning processes. Keep responses under 100 tokens.`
          },
          {
            role: 'user',
            content: `Context: ${context}\n\nPrompt: ${prompt}`
          }
        ],
        temperature: 0.8,
        max_completion_tokens: 100,
      });

      const processingTime = Date.now() - startTime;
      const thinking = completion.choices[0]?.message?.content || 'Processing...';

      // Log API usage and response details
      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);
      logOpenAIResponse(openaiLogger, operation, completion);

      logFunctionSuccess(openaiLogger, operation, {
        agentType,
        thinkingLength: thinking.length,
        totalTokens: completion.usage?.total_tokens,
      }, processingTime);

      openaiLogger.debug('Thinking content generated', {
        operation,
        phase: 'content',
        agentType,
        thinkingPreview: thinking.substring(0, 100) + (thinking.length > 100 ? '...' : ''),
      });

      return thinking;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      const fallbackThinking = agentType === 'therapist'
        ? 'Analyzing patient response and considering therapeutic approach...'
        : 'Processing therapist\'s words and formulating response...';

      logFunctionError(openaiLogger, operation, error, processingTime);

      openaiLogger.info('Returning fallback thinking due to error', {
        operation,
        phase: 'fallback',
        agentType,
        processingTime: `${processingTime}ms`,
      });

      return fallbackThinking;
    }
  }

  /**
   * Analyze sentiment of a message
   */
  async analyzeSentiment(message: string): Promise<'positive' | 'negative' | 'neutral'> {
    const startTime = Date.now();
    const operation = 'analyzeSentiment';

    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length,
    });

    try {
      openaiLogger.info('Starting sentiment analysis request', {
        operation,
        phase: 'api-call',
        model: this.defaultModel,
        temperature: 0.1,
        maxTokens: 10,
      });

      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'Analyze the sentiment of the following message. Respond with only one word: positive, negative, or neutral.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.1,
        max_completion_tokens: 10,
      });

      const processingTime = Date.now() - startTime;
      const sentiment = completion.choices[0]?.message?.content?.toLowerCase().trim();

      // Log API usage
      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);

      if (sentiment === 'positive' || sentiment === 'negative' || sentiment === 'neutral') {
        logFunctionSuccess(openaiLogger, operation, {
          sentiment,
          totalTokens: completion.usage?.total_tokens,
        }, processingTime);

        return sentiment;
      }

      // Invalid sentiment response, log warning and return default
      openaiLogger.warn('Invalid sentiment response from OpenAI', {
        operation,
        phase: 'invalid-response',
        receivedSentiment: sentiment,
        processingTime: `${processingTime}ms`,
      });

      return 'neutral';

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logFunctionError(openaiLogger, operation, error, processingTime);

      openaiLogger.info('Returning default sentiment due to error', {
        operation,
        phase: 'fallback',
        defaultSentiment: 'neutral',
        processingTime: `${processingTime}ms`,
      });

      return 'neutral';
    }
  }

  /**
   * Analyze engagement level
   */
  async analyzeEngagement(message: string): Promise<'low' | 'medium' | 'high'> {
    const startTime = Date.now();
    const operation = 'analyzeEngagement';

    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length,
    });

    try {
      openaiLogger.info('Starting engagement analysis request', {
        operation,
        phase: 'api-call',
        model: this.defaultModel,
        temperature: 0.1,
        maxTokens: 10,
      });

      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'Analyze the engagement level of this message in a therapy context. Consider factors like detail, emotional expression, and willingness to share. Respond with only one word: low, medium, or high.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.1,
        max_completion_tokens: 10,
      });

      const processingTime = Date.now() - startTime;
      const engagement = completion.choices[0]?.message?.content?.toLowerCase().trim();

      // Log API usage
      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);

      if (engagement === 'low' || engagement === 'medium' || engagement === 'high') {
        logFunctionSuccess(openaiLogger, operation, {
          engagement,
          totalTokens: completion.usage?.total_tokens,
        }, processingTime);

        return engagement;
      }

      // Invalid engagement response, log warning and return default
      openaiLogger.warn('Invalid engagement response from OpenAI', {
        operation,
        phase: 'invalid-response',
        receivedEngagement: engagement,
        processingTime: `${processingTime}ms`,
      });

      return 'medium';

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logFunctionError(openaiLogger, operation, error, processingTime);

      openaiLogger.info('Returning default engagement due to error', {
        operation,
        phase: 'fallback',
        defaultEngagement: 'medium',
        processingTime: `${processingTime}ms`,
      });

      return 'medium';
    }
  }

  /**
   * Analyze motivation level
   */
  async analyzeMotivation(message: string): Promise<'low' | 'medium' | 'high'> {
    const startTime = Date.now();
    const operation = 'analyzeMotivation';

    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length,
    });

    try {
      openaiLogger.info('Starting motivation analysis request', {
        operation,
        phase: 'api-call',
        model: this.defaultModel,
        temperature: 0.1,
        maxTokens: 10,
      });

      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'Analyze the motivation level for change and therapy engagement in this message. Consider willingness to work on issues, hope for improvement, and commitment to the process. Respond with only one word: low, medium, or high.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.1,
        max_completion_tokens: 10,
      });

      const processingTime = Date.now() - startTime;
      const motivation = completion.choices[0]?.message?.content?.toLowerCase().trim();

      // Log API usage
      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);

      if (motivation === 'low' || motivation === 'medium' || motivation === 'high') {
        logFunctionSuccess(openaiLogger, operation, {
          motivation,
          totalTokens: completion.usage?.total_tokens,
        }, processingTime);

        return motivation;
      }

      // Invalid motivation response, log warning and return default
      openaiLogger.warn('Invalid motivation response from OpenAI', {
        operation,
        phase: 'invalid-response',
        receivedMotivation: motivation,
        processingTime: `${processingTime}ms`,
      });

      return 'medium';

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logFunctionError(openaiLogger, operation, error, processingTime);

      openaiLogger.info('Returning default motivation due to error', {
        operation,
        phase: 'fallback',
        defaultMotivation: 'medium',
        processingTime: `${processingTime}ms`,
      });

      return 'medium';
    }
  }

  /**
   * Analyze sentiment intensity
   */
  async analyzeSentimentIntensity(message: string): Promise<'low' | 'medium' | 'high'> {
    const startTime = Date.now();
    const operation = 'analyzeSentimentIntensity';

    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length,
    });

    try {
      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'Analyze the intensity of emotion expressed in this message. Consider the strength of emotional language, exclamation marks, repetition, and overall emotional charge. Respond with only one word: low, medium, or high.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.1,
        max_completion_tokens: 10,
      });

      const processingTime = Date.now() - startTime;
      const intensity = completion.choices[0]?.message?.content?.toLowerCase().trim();

      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);

      if (intensity === 'low' || intensity === 'medium' || intensity === 'high') {
        logFunctionSuccess(openaiLogger, operation, { intensity }, processingTime);
        return intensity;
      }

      return 'medium';

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logFunctionError(openaiLogger, operation, error, processingTime);
      return 'medium';
    }
  }

  /**
   * Analyze motivation type (intrinsic vs extrinsic)
   */
  async analyzeMotivationType(message: string): Promise<'intrinsic' | 'extrinsic' | 'mixed'> {
    const startTime = Date.now();
    const operation = 'analyzeMotivationType';

    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length,
    });

    try {
      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'Analyze whether the motivation expressed in this message is primarily intrinsic (internal satisfaction, personal growth, values) or extrinsic (external rewards, avoiding punishment, pleasing others). Respond with only one word: intrinsic, extrinsic, or mixed.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.1,
        max_completion_tokens: 10,
      });

      const processingTime = Date.now() - startTime;
      const motivationType = completion.choices[0]?.message?.content?.toLowerCase().trim();

      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);

      if (motivationType === 'intrinsic' || motivationType === 'extrinsic' || motivationType === 'mixed') {
        logFunctionSuccess(openaiLogger, operation, { motivationType }, processingTime);
        return motivationType;
      }

      return 'mixed';

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logFunctionError(openaiLogger, operation, error, processingTime);
      return 'mixed';
    }
  }

  /**
   * Analyze engagement patterns
   */
  async analyzeEngagementPatterns(message: string): Promise<string[]> {
    const startTime = Date.now();
    const operation = 'analyzeEngagementPatterns';

    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length,
    });

    try {
      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'Identify engagement patterns in this therapy message. Look for: active participation, resistance, withdrawal, openness, defensiveness, curiosity, avoidance. Return a comma-separated list of observed patterns.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.1,
        max_completion_tokens: 50,
      });

      const processingTime = Date.now() - startTime;
      const patternsText = completion.choices[0]?.message?.content?.trim();

      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);

      if (patternsText) {
        const patterns = patternsText.split(',').map(p => p.trim()).filter(p => p.length > 0);
        logFunctionSuccess(openaiLogger, operation, { patterns }, processingTime);
        return patterns;
      }

      return ['neutral'];

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logFunctionError(openaiLogger, operation, error, processingTime);
      return ['neutral'];
    }
  }

  /**
   * Calculate confidence score based on OpenAI response
   */
  private calculateConfidence(completion: OpenAI.Chat.Completions.ChatCompletion): number {
    const operation = 'calculateConfidence';

    // Simple confidence calculation based on response characteristics
    const choice = completion.choices[0];
    if (!choice) {
      openaiLogger.debug('No choice available for confidence calculation', {
        operation,
        phase: 'no-choice',
      });
      return 0;
    }

    let confidence = 0.7; // Base confidence
    const messageLength = choice.message?.content?.length || 0;

    // Adjust based on finish reason
    if (choice.finish_reason === 'stop') {
      confidence += 0.2;
    } else if (choice.finish_reason === 'length') {
      confidence -= 0.1;
    }

    // Adjust based on response length (very short or very long responses might be less confident)
    if (messageLength > 50 && messageLength < 500) {
      confidence += 0.1;
    }

    const finalConfidence = Math.max(0, Math.min(1, confidence));

    openaiLogger.debug('Confidence calculated', {
      operation,
      phase: 'calculation',
      finishReason: choice.finish_reason,
      messageLength,
      baseConfidence: 0.7,
      finalConfidence,
    });

    return finalConfidence;
  }

  /**
   * Calculate readiness score for CBT based on comprehensive patient analysis
   */
  calculateReadinessScore(
    sentiment: 'positive' | 'negative' | 'neutral',
    motivation: 'low' | 'medium' | 'high',
    engagement: 'low' | 'medium' | 'high',
    sentimentIntensity?: 'low' | 'medium' | 'high',
    motivationType?: 'intrinsic' | 'extrinsic' | 'mixed',
    engagementPatterns?: string[]
  ): ReadinessScore {
    const operation = 'calculateReadinessScore';

    logFunctionEntry(openaiLogger, operation, {
      sentiment,
      motivation,
      engagement,
      sentimentIntensity,
      motivationType,
      engagementPatterns: engagementPatterns?.length || 0,
    });

    // Use defaults for optional parameters
    const intensity = sentimentIntensity || 'medium';
    const motType = motivationType || 'mixed';
    const patterns = engagementPatterns || ['neutral'];

    // Calculate weighted contributions
    const sentimentScore = readinessWeights.sentiment[sentiment];
    const intensityScore = readinessWeights.sentimentIntensity[intensity];
    const motivationScore = readinessWeights.motivation[motivation];
    const motivationTypeScore = readinessWeights.motivationType[motType];
    const engagementScore = readinessWeights.engagement[engagement];

    // Calculate engagement patterns score
    const positivePatterns = patterns.filter(p =>
      readinessWeights.engagementPatterns.positive.some(pos => p.toLowerCase().includes(pos))
    ).length;
    const negativePatterns = patterns.filter(p =>
      readinessWeights.engagementPatterns.negative.some(neg => p.toLowerCase().includes(neg))
    ).length;
    const patternsScore = Math.max(1, Math.min(3, 2 + positivePatterns - negativePatterns));

    const sentimentContribution = sentimentScore * readinessWeights.sentiment.weight;
    const intensityContribution = intensityScore * readinessWeights.sentimentIntensity.weight;
    const motivationContribution = motivationScore * readinessWeights.motivation.weight;
    const motivationTypeContribution = motivationTypeScore * readinessWeights.motivationType.weight;
    const engagementContribution = engagementScore * readinessWeights.engagement.weight;
    const patternsContribution = patternsScore * readinessWeights.engagementPatterns.weight;

    // Calculate final score (scale 1-10)
    const rawScore = sentimentContribution + intensityContribution + motivationContribution +
                     motivationTypeContribution + engagementContribution + patternsContribution;
    const normalizedScore = Math.round(((rawScore - 1) / 2) * 9 + 1); // Convert from 1-3 scale to 1-10 scale
    const finalScore = Math.max(1, Math.min(10, normalizedScore));

    // Determine recommended approach
    const recommendedApproach = finalScore >= 8 ? 'CBT' : 'MI';

    // Generate reasoning and indicators
    const reasoning = this.generateEnhancedReadinessReasoning(
      sentiment, motivation, engagement, intensity, motType, patterns, finalScore, recommendedApproach
    );
    const indicators = this.generateReadinessIndicators(
      sentiment, motivation, engagement, intensity, motType, patterns, finalScore
    );

    const readinessScore: ReadinessScore = {
      score: finalScore,
      factors: {
        sentiment: {
          value: sentiment,
          weight: readinessWeights.sentiment.weight,
          contribution: sentimentContribution
        },
        sentimentIntensity: {
          value: intensity,
          weight: readinessWeights.sentimentIntensity.weight,
          contribution: intensityContribution
        },
        motivation: {
          value: motivation,
          weight: readinessWeights.motivation.weight,
          contribution: motivationContribution
        },
        motivationType: {
          value: motType,
          weight: readinessWeights.motivationType.weight,
          contribution: motivationTypeContribution
        },
        engagement: {
          value: engagement,
          weight: readinessWeights.engagement.weight,
          contribution: engagementContribution
        },
        engagementPatterns: {
          value: patterns,
          weight: readinessWeights.engagementPatterns.weight,
          contribution: patternsContribution
        }
      },
      reasoning,
      recommendedApproach,
      indicators
    };

    logFunctionSuccess(openaiLogger, operation, {
      finalScore,
      recommendedApproach,
      sentimentContribution,
      motivationContribution,
      engagementContribution,
    });

    return readinessScore;
  }

  /**
   * Generate enhanced reasoning for readiness score
   */
  private generateEnhancedReadinessReasoning(
    sentiment: 'positive' | 'negative' | 'neutral',
    motivation: 'low' | 'medium' | 'high',
    engagement: 'low' | 'medium' | 'high',
    intensity: 'low' | 'medium' | 'high',
    motivationType: 'intrinsic' | 'extrinsic' | 'mixed',
    patterns: string[],
    score: number,
    approach: 'CBT' | 'MI'
  ): string {
    const sentimentDesc = {
      positive: 'positive emotional state',
      neutral: 'neutral emotional state',
      negative: 'negative emotional state'
    };

    const intensityDesc = {
      high: 'high emotional intensity',
      medium: 'moderate emotional intensity',
      low: 'low emotional intensity'
    };

    const motivationDesc = {
      high: 'high motivation for change',
      medium: 'moderate motivation for change',
      low: 'low motivation for change'
    };

    const motivationTypeDesc = {
      intrinsic: 'intrinsically motivated',
      extrinsic: 'extrinsically motivated',
      mixed: 'mixed motivation sources'
    };

    const engagementDesc = {
      high: 'high engagement in therapy',
      medium: 'moderate engagement in therapy',
      low: 'low engagement in therapy'
    };

    const approachReason = approach === 'CBT'
      ? 'Patient shows readiness for structured cognitive-behavioral interventions'
      : 'Patient would benefit from motivational interviewing to build readiness and motivation';

    const patternsDesc = patterns.length > 0 ? ` Engagement patterns include: ${patterns.join(', ')}.` : '';

    return `Score ${score}/10: Patient demonstrates ${sentimentDesc[sentiment]} with ${intensityDesc[intensity]}, ${motivationDesc[motivation]} (${motivationTypeDesc[motivationType]}), and ${engagementDesc[engagement]}.${patternsDesc} ${approachReason}.`;
  }

  /**
   * Generate readiness indicators
   */
  private generateReadinessIndicators(
    sentiment: 'positive' | 'negative' | 'neutral',
    motivation: 'low' | 'medium' | 'high',
    engagement: 'low' | 'medium' | 'high',
    intensity: 'low' | 'medium' | 'high',
    motivationType: 'intrinsic' | 'extrinsic' | 'mixed',
    patterns: string[],
    score: number
  ): { positive: string[]; negative: string[] } {
    const positive: string[] = [];
    const negative: string[] = [];

    // Sentiment indicators
    if (sentiment === 'positive') positive.push('Positive emotional state');
    if (sentiment === 'negative') negative.push('Negative emotional state');
    if (intensity === 'high' && sentiment === 'positive') positive.push('Strong positive emotions');
    if (intensity === 'high' && sentiment === 'negative') negative.push('Intense negative emotions');

    // Motivation indicators
    if (motivation === 'high') positive.push('High motivation for change');
    if (motivation === 'low') negative.push('Low motivation for change');
    if (motivationType === 'intrinsic') positive.push('Intrinsic motivation present');
    if (motivationType === 'extrinsic') negative.push('Primarily external motivation');

    // Engagement indicators
    if (engagement === 'high') positive.push('High therapy engagement');
    if (engagement === 'low') negative.push('Low therapy engagement');

    // Pattern indicators
    const positivePatterns = patterns.filter(p =>
      readinessWeights.engagementPatterns.positive.some(pos => p.toLowerCase().includes(pos))
    );
    const negativePatterns = patterns.filter(p =>
      readinessWeights.engagementPatterns.negative.some(neg => p.toLowerCase().includes(neg))
    );

    positivePatterns.forEach(p => positive.push(`Shows ${p}`));
    negativePatterns.forEach(p => negative.push(`Shows ${p}`));

    // Overall readiness indicators
    if (score >= 8) positive.push('Ready for structured interventions');
    if (score <= 4) negative.push('Needs motivation building');

    return { positive, negative };
  }

  /**
   * Perform comprehensive patient analysis including readiness score
   */
  async analyzePatient(message: string): Promise<PatientAnalysis> {
    const startTime = Date.now();
    const operation = 'analyzePatient';

    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length,
    });

    try {
      // Perform all analyses in parallel
      const [sentiment, motivation, engagement, sentimentIntensity, motivationType, engagementPatterns] = await Promise.all([
        this.analyzeSentiment(message),
        this.analyzeMotivation(message),
        this.analyzeEngagement(message),
        this.analyzeSentimentIntensity(message),
        this.analyzeMotivationType(message),
        this.analyzeEngagementPatterns(message)
      ]);

      // Calculate readiness score with enhanced analysis
      const readinessScore = this.calculateReadinessScore(
        sentiment, motivation, engagement, sentimentIntensity, motivationType, engagementPatterns
      );

      const analysis: PatientAnalysis = {
        sentiment,
        sentimentIntensity,
        motivationLevel: motivation,
        motivationType,
        engagementLevel: engagement,
        engagementPatterns,
        readinessScore
      };

      const processingTime = Date.now() - startTime;

      logFunctionSuccess(openaiLogger, operation, {
        sentiment,
        motivation,
        engagement,
        readinessScore: readinessScore.score,
        recommendedApproach: readinessScore.recommendedApproach,
      }, processingTime);

      return analysis;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logFunctionError(openaiLogger, operation, error, processingTime);

      // Return default analysis on error
      const defaultReadinessScore = this.calculateReadinessScore('neutral', 'medium', 'medium', 'medium', 'mixed', ['neutral']);

      return {
        sentiment: 'neutral',
        sentimentIntensity: 'medium',
        motivationLevel: 'medium',
        motivationType: 'mixed',
        engagementLevel: 'medium',
        engagementPatterns: ['neutral'],
        readinessScore: defaultReadinessScore
      };
    }
  }

  /**
   * Test the OpenAI connection
   */
  async testConnection(): Promise<boolean> {
    const startTime = Date.now();
    const operation = 'testConnection';

    logFunctionEntry(openaiLogger, operation);

    try {
      openaiLogger.info('Starting OpenAI connection test', {
        operation,
        phase: 'api-call',
        model: this.defaultModel,
        maxTokens: 5,
      });

      const completion = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [{ role: 'user', content: 'Hello' }],
        max_completion_tokens: 5,
      });

      const processingTime = Date.now() - startTime;
      const success = !!completion.choices[0]?.message?.content;

      // Log API usage
      logOpenAIUsage(openaiLogger, operation, completion.usage, processingTime);

      if (success) {
        logFunctionSuccess(openaiLogger, operation, {
          connectionStatus: 'successful',
          totalTokens: completion.usage?.total_tokens,
        }, processingTime);
      } else {
        openaiLogger.warn('OpenAI connection test returned empty response', {
          operation,
          phase: 'empty-response',
          processingTime: `${processingTime}ms`,
        });
      }

      return success;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      logFunctionError(openaiLogger, operation, error, processingTime);

      openaiLogger.error('OpenAI connection test failed', {
        operation,
        phase: 'connection-failed',
        processingTime: `${processingTime}ms`,
      });

      return false;
    }
  }
}
