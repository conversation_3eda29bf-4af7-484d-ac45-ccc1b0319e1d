// Test script to verify the multi-therapist TypeError fix
import { defaultConversationConfig } from './src/config/conversation';

function testConfigurationAccess() {
  console.log('🧪 Testing configuration access...');

  try {
    // Test 1: Verify the configuration structure
    console.log('1. Testing configuration structure...');
    const config = defaultConversationConfig.therapist;

    // This should work now (previously would throw TypeError)
    const readinessThresholds = config.therapeuticApproaches.readinessThresholds;
    const approachPreferences = config.therapeuticApproaches.approachPreferences;

    console.log('✅ Configuration access successful');
    console.log('📊 Readiness thresholds:', readinessThresholds);
    console.log('⚙️ Approach preferences:', approachPreferences);

    // Test 2: Verify the specific properties that were causing the error
    console.log('2. Testing specific properties...');
    const cbtMinimum = config.therapeuticApproaches.readinessThresholds.cbtMinimum;
    const miMaximum = config.therapeuticApproaches.readinessThresholds.miMaximum;
    const allowSwitching = config.therapeuticApproaches.approachPreferences.allowApproachSwitching;

    console.log('✅ Property access successful');
    console.log(`📈 CBT minimum threshold: ${cbtMinimum}`);
    console.log(`📉 MI maximum threshold: ${miMaximum}`);
    console.log(`🔄 Allow switching: ${allowSwitching}`);

    console.log('🎉 All tests passed! The TypeError has been resolved.');
    console.log('✨ MultiTherapistAgentService and MultiTherapistConversationOrchestrator should now initialize without errors.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
testConfigurationAccess();
