@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* CSS Custom Properties for Theme System */
:root {
  /* Light theme colors */
  --color-bg-primary: 250 250 250; /* neutral-50 */
  --color-bg-secondary: 255 255 255; /* white */
  --color-bg-tertiary: 245 245 245; /* neutral-100 */
  --color-text-primary: 23 23 23; /* neutral-900 */
  --color-text-secondary: 115 115 115; /* neutral-500 */
  --color-text-tertiary: 82 82 82; /* neutral-600 */
  --color-border-primary: 229 229 229; /* neutral-200 */
  --color-border-secondary: 212 212 212; /* neutral-300 */

  /* Primary colors */
  --color-primary-50: 240 249 255;
  --color-primary-100: 224 242 254;
  --color-primary-500: 14 165 233;
  --color-primary-600: 2 132 199;
  --color-primary-700: 3 105 161;

  /* Secondary colors */
  --color-secondary-50: 253 244 255;
  --color-secondary-100: 250 232 255;
  --color-secondary-500: 217 70 239;
  --color-secondary-600: 192 38 211;
  --color-secondary-700: 162 28 175;

  /* Status colors */
  --color-success: 34 197 94;
  --color-warning: 245 158 11;
  --color-error: 239 68 68;
}

/* Dark theme colors */
.dark {
  --color-bg-primary: 23 23 23; /* neutral-900 */
  --color-bg-secondary: 38 38 38; /* neutral-800 */
  --color-bg-tertiary: 64 64 64; /* neutral-700 */
  --color-text-primary: 250 250 250; /* neutral-50 */
  --color-text-secondary: 163 163 163; /* neutral-400 */
  --color-text-tertiary: 115 115 115; /* neutral-500 */
  --color-border-primary: 64 64 64; /* neutral-700 */
  --color-border-secondary: 82 82 82; /* neutral-600 */

  /* Primary colors (slightly adjusted for dark mode) */
  --color-primary-50: 23 37 84;
  --color-primary-100: 30 58 138;
  --color-primary-500: 59 130 246;
  --color-primary-600: 37 99 235;
  --color-primary-700: 29 78 216;

  /* Secondary colors (slightly adjusted for dark mode) */
  --color-secondary-50: 88 28 135;
  --color-secondary-100: 107 33 168;
  --color-secondary-500: 168 85 247;
  --color-secondary-600: 147 51 234;
  --color-secondary-700: 126 34 206;
}

/* Custom base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    /* Smooth theme transitions */
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  body {
    background-color: rgb(var(--color-bg-primary));
    color: rgb(var(--color-text-primary));
    @apply antialiased;
    /* Smooth theme transitions */
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  
  /* Custom scrollbar with theme support */
  ::-webkit-scrollbar {
    width: 0.5rem;
  }

  ::-webkit-scrollbar-track {
    background-color: rgb(var(--color-bg-tertiary));
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgb(var(--color-border-secondary));
    border-radius: 9999px;
    transition: background-color 0.3s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgb(var(--color-text-secondary));
  }
}

/* Custom component styles with theme support */
@layer components {
  .btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
    outline: none;
    border: 2px solid transparent;
  }

  .btn:focus {
    box-shadow: 0 0 0 2px rgb(var(--color-primary-500) / 0.5);
  }

  .btn-primary {
    background-color: rgb(var(--color-primary-600));
    color: white;
    transition: background-color 0.3s ease, transform 0.2s ease;
  }

  .btn-primary:hover {
    background-color: rgb(var(--color-primary-700));
    transform: translateY(-1px);
  }

  .btn-secondary {
    background-color: rgb(var(--color-bg-tertiary));
    color: rgb(var(--color-text-primary));
    border: 1px solid rgb(var(--color-border-primary));
    transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.2s ease;
  }

  .btn-secondary:hover {
    background-color: rgb(var(--color-border-primary));
    border-color: rgb(var(--color-border-secondary));
    transform: translateY(-1px);
  }

  .btn-danger {
    background-color: rgb(var(--color-error));
    color: white;
    transition: background-color 0.3s ease, transform 0.2s ease;
  }

  .btn-danger:hover {
    background-color: rgb(220 38 38); /* red-700 */
    transform: translateY(-1px);
  }

  .card {
    background-color: rgb(var(--color-bg-secondary));
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    border: 1px solid rgb(var(--color-border-primary));
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
  }

  .input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid rgb(var(--color-border-primary));
    border-radius: 0.5rem;
    background-color: rgb(var(--color-bg-secondary));
    color: rgb(var(--color-text-primary));
    outline: none;
    transition: border-color 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
  }

  .input:focus {
    border-color: rgb(var(--color-primary-500));
    box-shadow: 0 0 0 2px rgb(var(--color-primary-500) / 0.2);
  }

  .label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: rgb(var(--color-text-tertiary));
    margin-bottom: 0.25rem;
    transition: color 0.3s ease;
  }
  
  .message-bubble {
    max-width: 35rem;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  .message-therapist {
    background-color: rgb(var(--color-primary-100));
    color: rgb(var(--color-primary-700));
    margin-left: auto;
  }

  .message-patient {
    background-color: rgb(var(--color-bg-tertiary));
    color: rgb(var(--color-text-primary));
    margin-right: auto;
  }

  .thinking-panel {
    background-color: rgb(var(--color-bg-tertiary));
    border-left: 4px solid rgb(var(--color-border-secondary));
    padding: 1rem;
    font-size: 0.875rem;
    color: rgb(var(--color-text-secondary));
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  }

  .thinking-therapist {
    background-color: rgb(var(--color-primary-50));
    border-left-color: rgb(var(--color-primary-500));
    color: rgb(var(--color-primary-700));
  }

  .thinking-patient {
    background-color: rgb(var(--color-secondary-50));
    border-left-color: rgb(var(--color-secondary-500));
    color: rgb(var(--color-secondary-700));
  }

  /* Profile section styles */
  .profile-section {
    /* background-color: rgb(var(--color-bg-tertiary)); */
    /* border: 1px solid rgb(var(--color-border-primary)); */
    /* border-radius: 0.5rem; */
    /* padding: 0.5rem; */
    margin-bottom: 1rem;
    transition: background-color 0.3s ease, border-color 0.3s ease;
  }

  .profile-header {
    display: flex;
    align-items: center;
    /* margin-bottom: 0.75rem; */
  }

  .profile-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: rgb(var(--color-primary-500));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    margin-right: 0.75rem;
    font-size: 1rem;
  }

  .profile-avatar.patient {
    background-color: rgb(var(--color-secondary-500));
  }

  .profile-name {
    font-size: 1.2rem;
    font-weight: 800;
    color: rgb(var(--color-text-primary));
    margin: 0;
    transition: color 0.3s ease;
  }

  .profile-title {
    font-size: 0.75rem;
    color: rgb(var(--color-text-secondary));
    margin: 0;
    transition: color 0.3s ease;
  }

  .profile-details {
    display: grid;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: rgb(var(--color-text-secondary));
    transition: color 0.3s ease;
  }

  .profile-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .profile-detail-label {
    font-weight: 500;
    color: rgb(var(--color-text-tertiary));
  }

  .profile-detail-value {
    color: rgb(var(--color-text-secondary));
  }

  /* Analytics section styles */
  .analytics-section {
    /* background-color: rgb(var(--color-bg-tertiary)); */
    /* border: 1px solid rgb(var(--color-border-primary)); */
    /* border-radius: 0.5rem; */
    padding: 1rem;
    /* margin-top: 1rem; */
    transition: background-color 0.3s ease, border-color 0.3s ease;
  }

  .analytics-header {
    font-size: 0.875rem;
    font-weight: 600;
    color: rgb(var(--color-text-primary));
    margin-bottom: 0.75rem;
    transition: color 0.3s ease;
  }

  .analytics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .analytics-column {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .analytics-column-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: rgb(var(--color-text-tertiary));
    text-align: center;
    margin-bottom: 0.25rem;
    transition: color 0.3s ease;
  }

  .analytics-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background-color: rgb(var(--color-bg-secondary));
    border-radius: 0.25rem;
    border: 1px solid rgb(var(--color-border-primary));
    transition: background-color 0.3s ease, border-color 0.3s ease;
  }

  .analytics-metric-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: rgb(var(--color-text-tertiary));
    transition: color 0.3s ease;
  }

  .analytics-metric-value {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    text-transform: uppercase;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  .analytics-metric-value.positive {
    background-color: rgb(220 252 231);
    color: rgb(21 128 61);
  }

  .dark .analytics-metric-value.positive {
    background-color: rgb(20 83 45 / 0.5);
    color: rgb(134 239 172);
  }

  .analytics-metric-value.negative {
    background-color: rgb(254 226 226);
    color: rgb(185 28 28);
  }

  .dark .analytics-metric-value.negative {
    background-color: rgb(127 29 29 / 0.5);
    color: rgb(252 165 165);
  }

  .analytics-metric-value.neutral {
    background-color: rgb(243 244 246);
    color: rgb(55 65 81);
  }

  .dark .analytics-metric-value.neutral {
    background-color: rgb(31 41 55);
    color: rgb(209 213 219);
  }

  .analytics-metric-value.high {
    background-color: rgb(219 234 254);
    color: rgb(29 78 216);
  }

  .dark .analytics-metric-value.high {
    background-color: rgb(30 58 138 / 0.5);
    color: rgb(147 197 253);
  }

  .analytics-metric-value.medium {
    background-color: rgb(220 252 231);
    color: rgb(21 128 61);
  }

  .dark .analytics-metric-value.medium {
    background-color: rgb(20 83 45 / 0.5);
    color: rgb(134 239 172);
  }

  .analytics-metric-value.low {
    background-color: rgb(254 249 195);
    color: rgb(161 98 7);
  }

  .dark .analytics-metric-value.low {
    background-color: rgb(133 77 14 / 0.5);
    color: rgb(253 224 71);
  }
}

/* Utility classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Theme transition utilities */
  .theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  }

  .theme-transition-fast {
    transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
  }

  .theme-transition-slow {
    transition: background-color 0.5s ease, color 0.5s ease, border-color 0.5s ease;
  }
}
